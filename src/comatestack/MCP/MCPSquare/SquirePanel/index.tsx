import {Flex, List} from 'antd';
import {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from '@emotion/styled';
import {apiGetSquareServerList} from '@/api/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';
import MCPEmpty from '@/design/MCP/MCPEmpty';
import {FilterValues, TabValues} from '../SquireFilter';
import {useLoadMore} from '../../MCPSpace/MCPListPanel/hooks';
import {ALL_LABELS, OTHER_LABELS} from '../SquireFilter/LabelsFilterContent';
import SquireMCPCard from './SquireMCPCard';

const Container = styled.div`
    flex: 1;
    overflow-y: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const PAGE_SIZE = 12;

const CARD_HEIGHT = 235;

const processEmptyText = (filterData?: FilterValues & TabValues) => {
    if (filterData?.keywords?.trim()
        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)
        || filterData?.serverSourceType
        || filterData?.serverProtocolType
    ) {
        return '暂无结果';
    }
    if (filterData?.favorite) {
        return '暂无收藏的MCP Server';
    }
    if (filterData?.isMine) {
        return '暂无发布的MCP Server';
    }
    return '暂无MCP Server';
};

const formatSearchParams = (searchParams?: FilterValues & TabValues) => {
    return {
        viewOrder: searchParams.viewOrder,
        publishOrder: searchParams.publishOrder,
        ...(searchParams.tab !== 'all' ? {[searchParams.tab]: true} : {}),
        labels: searchParams.labels?.includes(ALL_LABELS)
            ? undefined
            : searchParams.labels?.includes(OTHER_LABELS)
                ? '-1'
                : searchParams.labels?.map(label => label).join(','),
        keywords: searchParams?.keywords?.trim() || undefined,
        serverSourceType: searchParams?.serverSourceType === 'all' ? undefined : searchParams.serverSourceType,
        serverProtocolType: searchParams.serverProtocolType === 'all' ? undefined : searchParams.serverProtocolType,
    };
};

interface Props {
    searchParams?: FilterValues & TabValues; // Define the type according to your needs
}
const SquirePanel = ({searchParams}: Props) => {
    const [pageSize, setPageSize] = useState(PAGE_SIZE);
    const api = useCallback(
        (params: {current: number, limit: number}) => {
            return apiGetSquareServerList({
                ...formatSearchParams(searchParams),
                platformType: 'hub',
                size: params.limit,
                pn: params.current,
            });
        },
        [searchParams]
    );
    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);

    useEffect(
        () => {
            refresh();
        },
        [refresh]
    );

    const emptyText = useMemo(
        () => processEmptyText(searchParams),
        [searchParams]
    );

    useLayoutEffect(
        () => {
            const container = document.getElementById('scrollableDiv');
            const height = container?.clientHeight || 0;
            let rows = Math.ceil(height / CARD_HEIGHT);
            // 因为每行可以有3个或2个卡片，所以需要确保pagesizepageSize是2和3的公倍数，所以需要调整rows的值为偶数。
            rows = rows % 2 === 0 ? rows : rows + 1;
            const maxItems = rows * 3;
            if (maxItems > PAGE_SIZE) {
                setPageSize(maxItems);
            }
        },
        []
    );

    return (
        <Container id="scrollableDiv">
            <InfiniteScroll
                style={{overflow: 'none'}}
                dataLength={list.length || 0}
                next={loadMore}
                hasMore={total > list.length}
                loader={<Flex justify="center" align="center"><div>加载中...</div></Flex>}
                scrollableTarget="scrollableDiv"
            >
                {(
                    <List
                        grid={{
                            gutter: 20,
                            column: 2,
                            xs: 2,
                            sm: 3,
                            md: 3,
                            lg: 3,
                            xl: 3,
                            xxl: 4,
                        }}
                        dataSource={list}
                        rowKey="id"
                        renderItem={server => (
                            <List.Item>
                                <SquireMCPCard refresh={refresh} key={server.id} server={server} />
                            </List.Item>
                        )}
                        locale={{
                            emptyText: (
                                <MCPEmpty
                                    description={(
                                        <Flex justify="center">
                                            {emptyText}
                                        </Flex>
                                    )}
                                />
                            ),
                        }}
                    />
                )}
            </InfiniteScroll>
            <CreateMCPAppModal />
        </Container>
    );
};

export default SquirePanel;
